{"name": "bookamat-mcp", "version": "1.0.0", "main": "index.js", "type": "module", "bin": {"weather": "./build/index.js"}, "scripts": {"build": "tsc && chmod 755 build/index.js"}, "files": ["build"], "repository": {"type": "git", "url": "git+https://github.com/julianhandl/bookamat-mcp.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/julianhandl/bookamat-mcp/issues"}, "homepage": "https://github.com/julianhandl/bookamat-mcp#readme", "description": "", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.8", "typescript": "^5.8.3"}}